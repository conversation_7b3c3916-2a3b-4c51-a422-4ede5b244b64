'use server';

import { redirect } from 'next/navigation';
import { createSession, destroySession, getSession } from './session';

export interface SignInData {
	email: string;
	password: string;
}

export interface SignUpData {
	fullName: string;
	email: string;
	password: string;
}

export async function signIn(data: SignInData) {
	try {
		const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/signin`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(data),
		});

		if (!response.ok) {
			const error = await response.json();
			return { error: error.message || 'Sign in failed' };
		}

		const result = await response.json();
		
		// Create server-side session
		await createSession(result.accessToken, result.refreshToken);
		
		return { success: true };
	} catch (error) {
		console.error('Sign in error:', error);
		return { error: 'An unexpected error occurred' };
	}
}

export async function signUp(data: SignUpData) {
	try {
		const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/register`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(data),
		});

		if (!response.ok) {
			const error = await response.json();
			return { error: error.message || 'Registration failed' };
		}

		const result = await response.json();
		
		// Auto-login after successful registration
		await createSession(result.accessToken, result.refreshToken);
		
		return { success: true };
	} catch (error) {
		console.error('Sign up error:', error);
		return { error: 'An unexpected error occurred' };
	}
}

export async function signOut() {
	try {
		const session = await getSession();
		
		if (session) {
			// Call API to invalidate tokens
			await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/logout`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${session.accessToken}`,
				},
				body: JSON.stringify({
					userId: session.user.id,
					refreshToken: session.refreshToken,
				}),
			});
		}
	} catch (error) {
		console.error('Sign out API error:', error);
		// Continue with local cleanup even if API call fails
	} finally {
		// Always destroy local session
		await destroySession();
	}
}

// Server action for sign out with redirect
export async function signOutAction() {
	await signOut();
	redirect('/signin');
}
