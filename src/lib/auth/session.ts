import { cookies } from 'next/headers';
import * as jose from 'jose';

export interface User {
	id: string;
	email: string;
	fullName: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

export interface SessionData {
	user: User;
	accessToken: string;
	refreshToken: string;
}

const JWT_SECRET = process.env.NEXT_PUBLIC_JWT_SECRET;
const COOKIE_NAME = 'auth-token';
const REFRESH_COOKIE_NAME = 'refresh-token';

if (!JWT_SECRET) {
	throw new Error('NEXT_PUBLIC_JWT_SECRET is not defined');
}

const secret = new TextEncoder().encode(JWT_SECRET);

export async function getSession(): Promise<SessionData | null> {
	try {
		const cookieStore = cookies();
		const token = cookieStore.get(COOKIE_NAME)?.value;
		const refreshToken = cookieStore.get(REFRESH_COOKIE_NAME)?.value;

		if (!token) {
			return null;
		}

		const { payload } = await jose.jwtVerify(token, secret);
		
		// Validate token structure
		if (payload.type !== 'auth' || !payload.email || !payload.role || !payload.status) {
			return null;
		}

		const user: User = {
			id: payload.id as string,
			email: payload.email as string,
			fullName: payload.fullName as string,
			role: payload.role as 'ADMIN' | 'USER',
			status: payload.status as 'ACTIVE' | 'INACTIVE' | 'REJECTED',
		};

		return {
			user,
			accessToken: token,
			refreshToken: refreshToken || '',
		};
	} catch (error) {
		console.error('Session validation error:', error);
		return null;
	}
}

export async function createSession(accessToken: string, refreshToken: string): Promise<void> {
	const cookieStore = cookies();
	
	// Set HTTP-only cookies
	cookieStore.set(COOKIE_NAME, accessToken, {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax',
		maxAge: 60 * 60 * 24 * 7, // 7 days
		path: '/',
	});

	cookieStore.set(REFRESH_COOKIE_NAME, refreshToken, {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax',
		maxAge: 60 * 60 * 24 * 30, // 30 days
		path: '/',
	});
}

export async function destroySession(): Promise<void> {
	const cookieStore = cookies();
	
	cookieStore.delete(COOKIE_NAME);
	cookieStore.delete(REFRESH_COOKIE_NAME);
}

export async function refreshSession(): Promise<SessionData | null> {
	try {
		const cookieStore = cookies();
		const refreshToken = cookieStore.get(REFRESH_COOKIE_NAME)?.value;

		if (!refreshToken) {
			return null;
		}

		// Call your API to refresh the token
		const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ refreshToken }),
		});

		if (!response.ok) {
			await destroySession();
			return null;
		}

		const data = await response.json();
		await createSession(data.accessToken, data.refreshToken);
		
		return await getSession();
	} catch (error) {
		console.error('Token refresh error:', error);
		await destroySession();
		return null;
	}
}
