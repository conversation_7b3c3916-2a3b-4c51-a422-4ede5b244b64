'use client';

import {
	createContext,
	useContext,
	ReactNode,
	useState,
	useEffect,
} from 'react';
import { useRouter } from 'next/router';

export interface User {
	id: string;
	email: string;
	fullName: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

interface AuthContextType {
	user: User | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	login: (
		email: string,
		password: string,
	) => Promise<{ success: boolean; error?: string }>;
	register: (
		fullName: string,
		email: string,
		password: string,
	) => Promise<{ success: boolean; error?: string }>;
	logout: () => Promise<void>;
	refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
	children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
	const [user, setUser] = useState<User | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const router = useRouter();

	// Decode JWT token to get user info
	const decodeToken = (token: string): User | null => {
		try {
			const payload = JSON.parse(atob(token.split('.')[1]));
			if (payload.type !== 'auth') return null;

			return {
				id: payload.id,
				email: payload.email,
				fullName: payload.fullName,
				role: payload.role,
				status: payload.status,
			};
		} catch {
			return null;
		}
	};

	// Initialize auth state from localStorage
	useEffect(() => {
		const initAuth = () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				const userData = decodeToken(token);
				if (userData) {
					setUser(userData);
				} else {
					// Invalid token, clear storage
					localStorage.removeItem('accessToken');
					localStorage.removeItem('refreshToken');
				}
			}
			setIsLoading(false);
		};

		initAuth();
	}, []);

	const login = async (email: string, password: string) => {
		try {
			setIsLoading(true);
			const response = await fetch('/api/auth/signin', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ email, password }),
			});

			const data = await response.json();

			if (!response.ok) {
				return { success: false, error: data.message || 'Login failed' };
			}

			// Store tokens
			localStorage.setItem('accessToken', data.accessToken);
			localStorage.setItem('refreshToken', data.refreshToken);

			// Decode and set user
			const userData = decodeToken(data.accessToken);
			if (userData) {
				setUser(userData);
				return { success: true };
			} else {
				return { success: false, error: 'Invalid token received' };
			}
		} catch (error) {
			return { success: false, error: 'Network error' };
		} finally {
			setIsLoading(false);
		}
	};

	const register = async (
		fullName: string,
		email: string,
		password: string,
	) => {
		try {
			setIsLoading(true);
			const response = await fetch('/api/auth/register', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ fullName, email, password }),
			});

			const data = await response.json();

			if (!response.ok) {
				return { success: false, error: data.message || 'Registration failed' };
			}

			// Auto-login after successful registration
			localStorage.setItem('accessToken', data.accessToken);
			localStorage.setItem('refreshToken', data.refreshToken);

			const userData = decodeToken(data.accessToken);
			if (userData) {
				setUser(userData);
				return { success: true };
			} else {
				return { success: false, error: 'Invalid token received' };
			}
		} catch (error) {
			return { success: false, error: 'Network error' };
		} finally {
			setIsLoading(false);
		}
	};

	const logout = async () => {
		try {
			const accessToken = localStorage.getItem('accessToken');
			const refreshToken = localStorage.getItem('refreshToken');

			if (accessToken && user) {
				// Call logout API
				await fetch('/api/auth/logout', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${accessToken}`,
					},
					body: JSON.stringify({
						userId: user.id,
						refreshToken,
					}),
				});
			}
		} catch (error) {
			console.error('Logout API error:', error);
		} finally {
			// Always clear local state
			localStorage.removeItem('accessToken');
			localStorage.removeItem('refreshToken');
			setUser(null);
			router.push('/signin');
		}
	};

	const refreshAuth = async () => {
		try {
			const refreshToken = localStorage.getItem('refreshToken');
			if (!refreshToken) return;

			const response = await fetch('/api/auth/refresh', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ refreshToken }),
			});

			if (response.ok) {
				const data = await response.json();
				localStorage.setItem('accessToken', data.accessToken);
				localStorage.setItem('refreshToken', data.refreshToken);

				const userData = decodeToken(data.accessToken);
				if (userData) {
					setUser(userData);
				}
			} else {
				// Refresh failed, logout
				await logout();
			}
		} catch (error) {
			console.error('Token refresh error:', error);
			await logout();
		}
	};

	const value: AuthContextType = {
		user,
		isAuthenticated: !!user,
		isLoading,
		login,
		register,
		logout,
		refreshAuth,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuth must be used within an AuthProvider');
	}
	return context;
}

// Hook for checking specific permissions
export function usePermissions() {
	const { user } = useAuth();

	return {
		isAdmin: user?.role === 'ADMIN',
		isUser: user?.role === 'USER',
		isActive: user?.status === 'ACTIVE',
		isInactive: user?.status === 'INACTIVE',
		isRejected: user?.status === 'REJECTED',
		canAccess: (requiredRoles: string[], requiredStatuses: string[]) => {
			if (!user) return false;
			return (
				requiredRoles.includes(user.role) &&
				requiredStatuses.includes(user.status)
			);
		},
	};
}
