'use client';

import { createContext, useContext, ReactNode } from 'react';
import { User } from './session';

interface AuthContextType {
	user: User | null;
	isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
	children: ReactNode;
	user: User | null;
}

export function AuthProvider({ children, user }: AuthProviderProps) {
	const value: AuthContextType = {
		user,
		isAuthenticated: !!user,
	};

	return (
		<AuthContext.Provider value={value}>
			{children}
		</AuthContext.Provider>
	);
}

export function useAuth(): AuthContextType {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuth must be used within an AuthProvider');
	}
	return context;
}

// Hook for checking specific permissions
export function usePermissions() {
	const { user } = useAuth();

	return {
		isAdmin: user?.role === 'ADMIN',
		isUser: user?.role === 'USER',
		isActive: user?.status === 'ACTIVE',
		isInactive: user?.status === 'INACTIVE',
		isRejected: user?.status === 'REJECTED',
		canAccess: (requiredRoles: string[], requiredStatuses: string[]) => {
			if (!user) return false;
			return requiredRoles.includes(user.role) && requiredStatuses.includes(user.status);
		},
	};
}
