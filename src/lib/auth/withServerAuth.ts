import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { getSession, User } from './session';

export interface AuthPageProps {
	user: User | null;
}

export interface AuthOptions {
	requireAuth?: boolean;
	requiredRoles?: ('ADMIN' | 'USER')[];
	requiredStatuses?: ('ACTIVE' | 'INACTIVE' | 'REJECTED')[];
	redirectTo?: string;
}

export function withServerAuth<P extends AuthPageProps = AuthPageProps>(
	getServerSidePropsFunc?: GetServerSideProps<P>,
	options: AuthOptions = {}
): GetServerSideProps<P> {
	return async (context: GetServerSidePropsContext) => {
		const {
			requireAuth = false,
			requiredRoles = ['ADMIN', 'USER'],
			requiredStatuses = ['ACTIVE'],
			redirectTo = '/signin',
		} = options;

		// Get session from server-side
		const session = await getSession();
		const user = session?.user || null;

		// Check authentication requirement
		if (requireAuth && !user) {
			return {
				redirect: {
					destination: `${redirectTo}?redirect=${encodeURIComponent(context.resolvedUrl)}`,
					permanent: false,
				},
			};
		}

		// Check role authorization
		if (user && requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
			return {
				redirect: {
					destination: '/unauthorized',
					permanent: false,
				},
			};
		}

		// Check status authorization
		if (user && requiredStatuses.length > 0 && !requiredStatuses.includes(user.status)) {
			// Redirect inactive/rejected users to account status page
			if (user.status === 'INACTIVE' || user.status === 'REJECTED') {
				return {
					redirect: {
						destination: '/account-status',
						permanent: false,
					},
				};
			}
			// For other status issues, redirect to unauthorized
			return {
				redirect: {
					destination: '/unauthorized',
					permanent: false,
				},
			};
		}

		// If authenticated user tries to access auth pages, redirect to home
		if (user && (context.resolvedUrl.startsWith('/signin') || context.resolvedUrl.startsWith('/register'))) {
			return {
				redirect: {
					destination: '/',
					permanent: false,
				},
			};
		}

		// Call the original getServerSideProps if provided
		let additionalProps = {};
		if (getServerSidePropsFunc) {
			const result = await getServerSidePropsFunc(context);
			
			if ('redirect' in result || 'notFound' in result) {
				return result;
			}
			
			additionalProps = result.props;
		}

		// Return props with user data
		return {
			props: {
				user,
				...additionalProps,
			} as P,
		};
	};
}
