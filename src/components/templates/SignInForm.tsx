import { <PERSON>, <PERSON>, Fields<PERSON>, Legend } from '@headlessui/react';
import { Input, <PERSON><PERSON>, <PERSON><PERSON>, ButtonLoader } from '@/components/atoms';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/providers/auth-store-provider';
import { AuthAPI } from '@/api/AuthApi';
import { Httpstatus } from '@/common/StandardApi';

export default function SignInForm() {
	const [message, setMessage] = useState<string | null>(null);
	const router = useRouter();
	const isLoading = useAuthStore((state) => state.isLoading);
	const error = useAuthStore((state) => state.error);
	const clearError = useAuthStore((state) => state.clearError);
	const setTokens = useAuthStore((state) => state.setTokens);

	useEffect(() => {
		const urlMessage = router.query.message as string;
		if (urlMessage) {
			setMessage(urlMessage);
		}
	}, [router.query.message]);

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		clearError();

		const formData = new FormData(event.currentTarget);
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;

		if (!email || !password) {
			return;
		}

		try {
			const authApi = new AuthAPI();
			const response = await authApi.login({ email, password });

			if (
				response.status === Httpstatus.SuccessOK ||
				response.status === Httpstatus.SuccessCreated
			) {
				// Extract tokens from response
				const { accessToken, refreshToken } = response.data.data;

				if (!accessToken || !refreshToken) {
					throw new Error('Invalid response: missing tokens');
				}

				// Use the store's setTokens method
				await setTokens(accessToken, refreshToken);

				// Redirect to intended page or home
				const redirect = router.query.redirect as string;
				router.push(redirect || '/');
			} else {
				throw new Error(response.data?.message || 'Login failed');
			}
		} catch (error) {
			console.error('Login error:', error);
			// The error will be handled by the store's setTokens method if it fails
		}
	};
	return (
		<div>
			{message && (
				<Alert
					className='mb-4'
					variant='default'>
					{message}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg font-semibold text-foreground'>
						Sign in to your account
					</Legend>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					{isLoading && <ButtonLoader />}
					{isLoading ? 'Signing in...' : 'Sign in'}
				</Button>
			</form>
		</div>
	);
}
