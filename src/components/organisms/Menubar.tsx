import Link from 'next/link';
import { Button } from '@/components/atoms';
import { useAuthStore } from '@/stores/authStore';

interface MenubarProps {
	logo: React.ReactNode;
	links: {
		title: string;
		href: string;
		startIcon?: React.ReactNode;
		endIcon?: React.ReactNode;
		isActive?: boolean;
		onClick?: () => void;
		className?: string;
		disabled?: boolean;
	}[];
}

const Menubar: React.FC<MenubarProps> = ({ logo, links }) => {
	const { user, logout, isLoading } = useAuthStore();

	return (
		<div className='bg-card border-b border-border flex items-center justify-between h-16 px-4 shadow-sm'>
			<div className='flex items-center space-x-4'>
				<div className='flex-shrink-0'>{logo}</div>
				{links.map((link, index) => (
					<Link
						key={index}
						href={link.href}
						className={`flex items-center space-x-2 ${link.className}`}
						onClick={link.onClick}
						aria-disabled={link.disabled}>
						{link.startIcon && (
							<span className='flex-shrink-0'>{link.startIcon}</span>
						)}
						<span
							className={`text-sm ${
								link.isActive ? 'text-primary' : 'text-muted-foreground'
							}`}>
							{link.title}
						</span>
						{link.endIcon && (
							<span className='flex-shrink-0'>{link.endIcon}</span>
						)}
					</Link>
				))}
			</div>

			{user && (
				<div className='flex items-center space-x-4'>
					<span className='text-sm text-muted-foreground'>{user.fullName}</span>
					<Button
						variant='secondary'
						size='sm'
						onClick={logout}
						disabled={isLoading}>
						{isLoading ? 'Logging out...' : 'Logout'}
					</Button>
				</div>
			)}
		</div>
	);
};

Menubar.displayName = 'Menubar';
export default Menubar;
