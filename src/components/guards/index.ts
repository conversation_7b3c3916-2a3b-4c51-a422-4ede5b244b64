// Individual guards
export { withAuth } from './withAuth';
export { withRole, withAdminRole, withUserRole, withAnyRole } from './withRole';
export { withActiveStatus, withActiveStatusOnly } from './withActiveStatus';

// Combined guards (recommended for most use cases)
export {
	withGuards,
	withAuthAndActiveStatus,
	withAdminGuards,
	withUserGuards,
	withAnyRoleGuards,
	withAuthOnly,
} from './withGuards';

// Types for external use
export type UserRole = 'ADMIN' | 'USER';
export type UserStatus = 'ACTIVE' | 'INACTIVE' | 'REJECTED';
