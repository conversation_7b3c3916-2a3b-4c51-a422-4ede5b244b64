# Authentication and Authorization Guards

This directory contains Higher-Order Components (HOCs) for implementing authentication and authorization in the application.

## Overview

The guard system provides three levels of protection:

1. **Authentication**: Ensures user is logged in
2. **Role-based Access**: Restricts access based on user roles (ADMIN/USER)
3. **Status-based Access**: Blocks users with INACTIVE/REJECTED status

## Available Guards

### Individual Guards

#### `withAuth`
Ensures user is authenticated before accessing protected routes.

```tsx
import { withAuth } from '@/components/guards';

const ProtectedPage = () => <div>Protected content</div>;
export default withAuth(ProtectedPage);
```

#### `withRole`
Restricts access based on user roles.

```tsx
import { withRole } from '@/components/guards';

const AdminPage = () => <div>Admin only content</div>;
export default withRole({ allowedRoles: ['ADMIN'] })(AdminPage);
```

#### `withActiveStatus`
Blocks users with INACTIVE/REJECTED status while keeping them authenticated.

```tsx
import { withActiveStatus } from '@/components/guards';

const ActiveUserPage = () => <div>Active users only</div>;
export default withActiveStatus()(ActiveUserPage);
```

### Combined Guards (Recommended)

#### `withGuards`
The main HOC that combines all protection levels.

```tsx
import { withGuards } from '@/components/guards';

const SecurePage = () => <div>Secure content</div>;
export default withGuards({
  requireAuth: true,
  allowedRoles: ['ADMIN'],
  allowedStatuses: ['ACTIVE']
})(SecurePage);
```

#### Convenience Functions

```tsx
import { 
  withAdminGuards,
  withUserGuards,
  withAnyRoleGuards,
  withAuthAndActiveStatus,
  withAuthOnly
} from '@/components/guards';

// Admin only (authenticated + ADMIN role + ACTIVE status)
export default withAdminGuards(AdminPage);

// User only (authenticated + USER role + ACTIVE status)
export default withUserGuards(UserPage);

// Any role (authenticated + any role + ACTIVE status)
export default withAnyRoleGuards(GeneralPage);

// Auth + active status only
export default withAuthAndActiveStatus(ActivePage);

// Auth only (allows all statuses - useful for status pages)
export default withAuthOnly(StatusPage);
```

## Usage Examples

### Basic Protected Page
```tsx
import { withAnyRoleGuards } from '@/components/guards';

const DashboardPage = () => {
  return <div>Dashboard content</div>;
};

export default withAnyRoleGuards(DashboardPage);
```

### Admin-Only Page
```tsx
import { withAdminGuards } from '@/components/guards';

const AdminPanel = () => {
  return <div>Admin panel</div>;
};

export default withAdminGuards(AdminPanel);
```

### Custom Guard Configuration
```tsx
import { withGuards } from '@/components/guards';

const CustomPage = () => {
  return <div>Custom protected content</div>;
};

export default withGuards({
  requireAuth: true,
  allowedRoles: ['ADMIN', 'USER'],
  allowedStatuses: ['ACTIVE'],
  roleRedirectTo: '/unauthorized',
  statusRedirectTo: '/account-status'
})(CustomPage);
```

## User Status Handling

Users with different statuses are handled as follows:

- **ACTIVE**: Full access to the application
- **INACTIVE**: Redirected to `/account-status` page with explanation
- **REJECTED**: Redirected to `/account-status` page with rejection notice

## Error Pages

- `/unauthorized`: Shown when user lacks required role
- `/account-status`: Shown when user has INACTIVE/REJECTED status

## Best Practices

1. **Use convenience functions** for common scenarios:
   - `withAdminGuards` for admin-only pages
   - `withAnyRoleGuards` for general protected pages

2. **Apply guards at the page level**, not component level

3. **Use `withAuthOnly`** for pages that need to show status information to all authenticated users

4. **Combine with Next.js layouts** for consistent protection across route groups

5. **Test different user states** (roles and statuses) to ensure proper access control

## Implementation Notes

- Guards use the `useAuthStore` hook to access user state
- Authentication state is checked on every route change
- Guards wait for store hydration before making access decisions
- Loading states are shown while checking permissions
- Redirects preserve the original URL for post-login navigation
