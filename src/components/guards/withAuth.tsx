import { useEffect, ComponentType } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';
import { Loader } from '@/components/atoms';

// Safe router hook that handles SSR and hydration issues
const useRouterSafe = () => {
	try {
		const router = useRouter();
		// Check if router is ready (client-side only)
		return router.isReady ? router : null;
	} catch (error) {
		// Router not available (likely SSR or hydration issue)
		console.warn('Router not available:', error);
		return null;
	}
};

// Create a loading component that can be wrapped by layouts
const AuthLoadingComponent = ({ text }: { text: string }) => (
	<div className='flex items-center justify-center min-h-[400px]'>
		<Loader
			size='lg'
			text={text}
		/>
	</div>
);

/**
 * HOC that ensures user is authenticated before accessing protected routes
 * Redirects to signin page if user is not authenticated
 */
export function withAuth<P extends object>(
	WrappedComponent: ComponentType<P>,
): ComponentType<P> {
	const AuthenticatedComponent = (props: P) => {
		const router = useRouterSafe();
		const { user, accessToken, isHydrated } = useAuthStore();

		useEffect(() => {
			// Wait for store to hydrate and router to be ready
			if (!isHydrated || !router) return;

			// Check if user is authenticated
			if (!user || !accessToken) {
				// Store the current path for redirect after login
				const currentPath = router.asPath;
				const redirectUrl =
					currentPath !== '/' &&
					(currentPath.startsWith('/projects') ||
						currentPath.startsWith('/admin'))
						? currentPath
						: '/';

				router.replace(`/signin?redirect=${encodeURIComponent(redirectUrl)}`);
				return;
			}
		}, [user, accessToken, isHydrated, router]);

		// If not hydrated or router not ready, show loading within layout
		if (!isHydrated || !router) {
			return <AuthLoadingComponent text='Loading...' />;
		}

		// If hydrated but no user/token, show redirecting message within layout
		if (!user || !accessToken) {
			return <AuthLoadingComponent text='Redirecting to sign in...' />;
		}

		// User is authenticated, render the component
		return <WrappedComponent {...props} />;
	};

	// Set display name for debugging
	AuthenticatedComponent.displayName = `withAuth(${
		WrappedComponent.displayName || WrappedComponent.name
	})`;

	return AuthenticatedComponent;
}
