import { ComponentType } from 'react';
import { withAuth } from './withAuth';
import { withRole } from './withRole';
import { withActiveStatus } from './withActiveStatus';

type UserRole = 'ADMIN' | 'USER';
type UserStatus = 'ACTIVE' | 'INACTIVE' | 'REJECTED';

interface GuardOptions {
	// Authentication (always applied)
	requireAuth?: boolean;
	
	// Role-based access control
	allowedRoles?: UserRole[];
	roleRedirectTo?: string;
	roleFallbackComponent?: ComponentType;
	
	// Status-based access control
	allowedStatuses?: UserStatus[];
	statusRedirectTo?: string;
	statusFallbackComponent?: ComponentType;
}

/**
 * Combined HOC that applies authentication, role, and status guards
 * This is the main HOC you should use for most protected routes
 */
export function withGuards<P extends object>(
	options: GuardOptions = {}
): (WrappedComponent: ComponentType<P>) => ComponentType<P> {
	return function (WrappedComponent: ComponentType<P>): ComponentType<P> {
		const {
			requireAuth = true,
			allowedRoles,
			roleRedirectTo,
			roleFallbackComponent,
			allowedStatuses = ['ACTIVE'],
			statusRedirectTo,
			statusFallbackComponent,
		} = options;

		let GuardedComponent = WrappedComponent;

		// Apply status guard first (innermost)
		if (allowedStatuses) {
			GuardedComponent = withActiveStatus<P>({
				allowedStatuses,
				redirectTo: statusRedirectTo,
				fallbackComponent: statusFallbackComponent,
			})(GuardedComponent);
		}

		// Apply role guard
		if (allowedRoles) {
			GuardedComponent = withRole<P>({
				allowedRoles,
				redirectTo: roleRedirectTo,
				fallbackComponent: roleFallbackComponent,
			})(GuardedComponent);
		}

		// Apply auth guard last (outermost)
		if (requireAuth) {
			GuardedComponent = withAuth(GuardedComponent);
		}

		// Set display name for debugging
		const guardNames = [];
		if (requireAuth) guardNames.push('Auth');
		if (allowedRoles) guardNames.push(`Role(${allowedRoles.join(',')})`);
		if (allowedStatuses) guardNames.push(`Status(${allowedStatuses.join(',')})`);
		
		GuardedComponent.displayName = `withGuards[${guardNames.join('+')}](${WrappedComponent.displayName || WrappedComponent.name})`;

		return GuardedComponent;
	};
}

// Convenience functions for common guard combinations
export const withAuthAndActiveStatus = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withGuards<P>({
		requireAuth: true,
		allowedStatuses: ['ACTIVE'],
	})(WrappedComponent);

export const withAdminGuards = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withGuards<P>({
		requireAuth: true,
		allowedRoles: ['ADMIN'],
		allowedStatuses: ['ACTIVE'],
	})(WrappedComponent);

export const withUserGuards = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withGuards<P>({
		requireAuth: true,
		allowedRoles: ['USER'],
		allowedStatuses: ['ACTIVE'],
	})(WrappedComponent);

export const withAnyRoleGuards = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withGuards<P>({
		requireAuth: true,
		allowedRoles: ['ADMIN', 'USER'],
		allowedStatuses: ['ACTIVE'],
	})(WrappedComponent);

// For pages that need authentication but should show status messages to inactive/rejected users
export const withAuthOnly = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withGuards<P>({
		requireAuth: true,
		allowedStatuses: ['ACTIVE', 'INACTIVE', 'REJECTED'], // Allow all statuses
	})(WrappedComponent);
