'use client';

import { type ReactNode, createContext, useRef, useContext, useEffect } from 'react';
import { useStore } from 'zustand';
import { type AuthStore, createAuthStore } from '@/stores/authStore';

export type AuthStoreApi = ReturnType<typeof createAuthStore>;

export const AuthStoreContext = createContext<AuthStoreApi | undefined>(undefined);

export interface AuthStoreProviderProps {
	children: ReactNode;
}

export const AuthStoreProvider = ({ children }: AuthStoreProviderProps) => {
	const storeRef = useRef<AuthStoreApi | null>(null);
	
	if (storeRef.current === null) {
		storeRef.current = createAuthStore();
	}

	// Handle hydration on client side
	useEffect(() => {
		if (storeRef.current && typeof window !== 'undefined') {
			// Trigger hydration from localStorage
			const state = storeRef.current.getState();
			if (!state.isHydrated) {
				// Force hydration by checking localStorage
				const stored = localStorage.getItem('auth-storage');
				if (stored) {
					try {
						const parsedState = JSON.parse(stored);
						if (parsedState.state) {
							storeRef.current.setState({
								...parsedState.state,
								isHydrated: true,
							});
						}
					} catch (error) {
						console.error('Failed to hydrate auth store:', error);
						storeRef.current.setState({ isHydrated: true });
					}
				} else {
					storeRef.current.setState({ isHydrated: true });
				}
			}
		}
	}, []);

	return (
		<AuthStoreContext.Provider value={storeRef.current}>
			{children}
		</AuthStoreContext.Provider>
	);
};

export const useAuthStore = <T,>(selector: (store: AuthStore) => T): T => {
	const authStoreContext = useContext(AuthStoreContext);

	if (!authStoreContext) {
		throw new Error('useAuthStore must be used within AuthStoreProvider');
	}

	return useStore(authStoreContext, selector);
};
