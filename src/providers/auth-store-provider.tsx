'use client';

import { type ReactNode, createContext, useRef, useContext } from 'react';
import { useStore } from 'zustand';
import { type AuthStore, createAuthStore } from '@/stores/authStore';

export type AuthStoreApi = ReturnType<typeof createAuthStore>;

export const AuthStoreContext = createContext<AuthStoreApi | undefined>(
	undefined,
);

// Global store reference for non-React contexts (like API calls)
let globalAuthStore: AuthStoreApi | null = null;

export interface AuthStoreProviderProps {
	children: ReactNode;
}

export const AuthStoreProvider = ({ children }: AuthStoreProviderProps) => {
	const storeRef = useRef<AuthStoreApi | null>(null);

	if (storeRef.current === null) {
		storeRef.current = createAuthStore();
		// Set global reference for non-React contexts
		globalAuthStore = storeRef.current;
	}

	return (
		<AuthStoreContext.Provider value={storeRef.current}>
			{children}
		</AuthStoreContext.Provider>
	);
};

export const useAuthStore = <T,>(selector: (store: AuthStore) => T): T => {
	const authStoreContext = useContext(AuthStoreContext);

	if (!authStoreContext) {
		throw new Error('useAuthStore must be used within AuthStoreProvider');
	}

	return useStore(authStoreContext, selector);
};

// Function to get auth store outside of React components (for API calls)
export const getAuthStore = () => {
	if (!globalAuthStore) {
		throw new Error(
			'Auth store not initialized. Make sure AuthStoreProvider is mounted.',
		);
	}
	return globalAuthStore;
};
