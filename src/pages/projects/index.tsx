import { SideBar } from '@/components/organisms';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '@/pages/_app';
import { withServerAuth, AuthPageProps } from '@/lib/auth/withServerAuth';
import {
	CircleStackIcon,
	FolderIcon,
	UserCircleIcon,
} from '@heroicons/react/24/outline';
import { useState } from 'react';

const ProjectsPage: NextPageWithLayout<AuthPageProps> = () => {
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const items = [
		{
			label: 'Project 1',
			icon: <FolderIcon />,
		},
		{
			label: 'Project 2',
			icon: <UserCircleIcon />,
		},
	];
	return (
		<div className='flex h-svh'>
			<SideBar
				title='Projects'
				sections={[
					{
						title: 'My Projects',
						items,
					},
					{
						title: 'Archived Projects',
						items: [
							{
								label: 'Archived Project 1',
								icon: <CircleStackIcon />,
								onClick: () => console.log('Archived Project 1 clicked'),
							},
						],
					},
				]}
				isCollapsed={isSidebarCollapsed}
				onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
			/>
			<main className='flex-1 p-4'>
				<h1>Projects</h1>
				<p>Your projects will be displayed here.</p>
			</main>
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export const getServerSideProps = withServerAuth(undefined, {
	requireAuth: true,
	requiredRoles: ['ADMIN', 'USER'],
	requiredStatuses: ['ACTIVE'],
});

export default ProjectsPage;
