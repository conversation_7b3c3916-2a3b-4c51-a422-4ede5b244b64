import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from './_app';
// import { withAnyRoleGuards } from '@/components/guards';

const HomePage: NextPageWithLayout = () => {
	return (
		<main>
			<h1>Recent projects:</h1>
		</main>
	);
};

HomePage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

// Temporarily removing guards for debugging
export default HomePage;
// export default withAnyRoleGuards(HomePage);
