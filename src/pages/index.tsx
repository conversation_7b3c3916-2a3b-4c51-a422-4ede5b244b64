import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from './_app';
import { withServerAuth, AuthPageProps } from '@/lib/auth/withServerAuth';

const HomePage: NextPageWithLayout<AuthPageProps> = () => {
	return (
		<main>
			<h1>Recent projects:</h1>
		</main>
	);
};

HomePage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export const getServerSideProps = withServerAuth(undefined, {
	requireAuth: true,
	requiredRoles: ['ADMIN', 'USER'],
	requiredStatuses: ['ACTIVE'],
});

export default HomePage;
