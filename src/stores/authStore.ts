import { createStore } from 'zustand/vanilla';
import { persist, createJSONStorage } from 'zustand/middleware';
import * as jose from 'jose';
import { setAuthCookie, removeAuthCookie } from '@/common/utils/cookies';

export interface User {
	id: string;
	fullName: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

interface TokenPayload {
	email: string;
	sub: string; // User ID
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	type: 'auth';
	exp: number;
	iat: number;
}

export type AuthState = {
	user: User | null;
	accessToken: string | null;
	refreshToken: string | null;
	isLoading: boolean;
	error: string | null;
	isHydrated: boolean;
};

export type AuthActions = {
	setTokens: (accessToken: string, refreshToken: string) => Promise<void>;
	setUser: (user: User) => void;
	logout: () => Promise<void>;
	clearError: () => void;
	setLoading: (loading: boolean) => void;
	setError: (error: string) => void;
	refreshTokens: () => Promise<boolean>;
	_setHydrated: (hydrated: boolean) => void;
	_clearAllData: () => void;
};

export type AuthStore = AuthState & AuthActions;

export const defaultInitState: AuthState = {
	user: null,
	accessToken: null,
	refreshToken: null,
	isLoading: false,
	error: null,
	isHydrated: false,
};

const decodeToken = async (
	token: string | null | undefined,
): Promise<User | null> => {
	try {
		// Check if token is valid
		if (!token || typeof token !== 'string' || token.trim() === '') {
			return null;
		}

		// Get JWT secret
		const secret = process.env.NEXT_PUBLIC_JWT_SECRET;
		if (!secret) {
			console.error('NEXT_PUBLIC_JWT_SECRET is not defined');
			return null;
		}
		const secretKey = new TextEncoder().encode(secret);

		// Verify and decode the JWT
		const { payload } = await jose.jwtVerify(token, secretKey);

		// Validate payload structure
		if (
			typeof payload.email !== 'string' ||
			typeof payload.sub !== 'string' ||
			typeof payload.role !== 'string' ||
			typeof payload.status !== 'string' ||
			payload.type !== 'auth'
		) {
			console.error('Invalid token payload structure:', payload);
			return null;
		}

		// Type assertion for the payload (now safe after validation)
		const typedPayload = payload as unknown as TokenPayload;

		return {
			id: typedPayload.sub, // Use sub field as the actual user ID
			fullName: typedPayload.email.split('@')[0], // Extract username from email as fallback
			email: typedPayload.email,
			role: typedPayload.role,
			status: typedPayload.status,
		};
	} catch (error) {
		// Handle specific JWT errors
		if (error instanceof Error) {
			if (error.name === 'JWTExpired') {
				console.log('Token has expired');
				return null;
			} else if (error.name === 'JWTInvalid') {
				console.log('Token is invalid');
				return null;
			} else {
				console.error('Failed to decode token:', error.message);
			}
		} else {
			console.error('Failed to decode token:', error);
		}
		return null;
	}
};

export const createAuthStore = (initState: AuthState = defaultInitState) => {
	return createStore<AuthStore>()(
		persist(
			(set, get) => ({
				...initState,

				setTokens: async (accessToken: string, refreshToken: string) => {
					try {
						// Validate tokens are strings
						if (!accessToken || typeof accessToken !== 'string') {
							console.error('Invalid access token:', {
								accessToken,
								type: typeof accessToken,
							});
							set({
								accessToken: null,
								refreshToken: null,
								user: null,
								error: 'Access token must be a non-empty string',
								isLoading: false,
							});
							return;
						}

						if (!refreshToken || typeof refreshToken !== 'string') {
							console.error('Invalid refresh token:', {
								refreshToken,
								type: typeof refreshToken,
							});
							set({
								accessToken: null,
								refreshToken: null,
								user: null,
								error: 'Refresh token must be a non-empty string',
								isLoading: false,
							});
							return;
						}

						const user = await decodeToken(accessToken);

						if (!user) {
							set({
								accessToken: null,
								refreshToken: null,
								user: null,
								error: 'Invalid or expired access token',
								isLoading: false,
							});
							return;
						}

						// Set cookie for middleware
						setAuthCookie(accessToken);

						set({
							accessToken,
							refreshToken,
							user,
							error: null,
							isLoading: false,
						});
					} catch (error) {
						console.error('Error setting tokens:', error);
						set({
							accessToken: null,
							refreshToken: null,
							user: null,
							error:
								error instanceof Error
									? error.message
									: 'Invalid tokens provided',
							isLoading: false,
						});
					}
				},

				setUser: (user: User) => {
					set({ user, error: null, isLoading: false });
				},

				logout: async () => {
					set({ isLoading: true });

					try {
						// Call logout API if we have a user
						const { user } = get();
						if (user?.id) {
							const { AuthAPI } = await import('@/api/AuthApi');
							const authApi = new AuthAPI();
							await authApi.logout(user.id);
						}
					} catch (error) {
						console.error('Logout API call failed:', error);
						// Continue with logout even if API call fails
					}

					// Clear all auth data
					set({
						user: null,
						accessToken: null,
						refreshToken: null,
						isLoading: false,
						error: null,
					});

					// Remove auth cookie
					removeAuthCookie();

					// Redirect to signin
					if (typeof window !== 'undefined') {
						window.location.href = '/signin';
					}
				},

				clearError: () => set({ error: null }),

				setLoading: (loading: boolean) => set({ isLoading: loading }),

				setError: (error: string) => set({ error, isLoading: false }),

				refreshTokens: async (): Promise<boolean> => {
					const { user } = get();
					if (!user?.id) return false;

					try {
						const { AuthAPI } = await import('@/api/AuthApi');
						const authApi = new AuthAPI();
						const response = await authApi.refresh(user.id);

						if (response.status === 200 && response.data?.data) {
							const tokenData = response.data.data;
							// According to backend API spec, refresh returns token wrapped in 'value' property
							const newAccessToken = tokenData.value;

							if (newAccessToken && typeof newAccessToken === 'string') {
								// Keep the existing refresh token since only access token is refreshed
								const { refreshToken } = get();
								await get().setTokens(newAccessToken, refreshToken || '');
								return true;
							} else {
								console.error(
									'Invalid token format in refresh response:',
									tokenData,
								);
								await get().logout();
								return false;
							}
						}

						// Refresh failed, logout
						await get().logout();
						return false;
					} catch (error) {
						console.error('Token refresh failed:', error);
						await get().logout();
						return false;
					}
				},

				_setHydrated: (hydrated: boolean) => set({ isHydrated: hydrated }),

				_clearAllData: () => {
					// Clear all auth data
					set({
						user: null,
						accessToken: null,
						refreshToken: null,
						isLoading: false,
						error: null,
					});

					// Clear cookies
					removeAuthCookie();

					// Clear localStorage
					if (typeof window !== 'undefined') {
						localStorage.removeItem('auth-storage');
					}
				},
			}),
			{
				name: 'auth-storage',
				storage: createJSONStorage(() => {
					// Only use localStorage on client side
					if (typeof window !== 'undefined') {
						return localStorage;
					}
					// Return a no-op storage for SSR
					return {
						getItem: () => null,
						setItem: () => {},
						removeItem: () => {},
					};
				}),
				partialize: (state) => ({
					accessToken: state.accessToken,
					refreshToken: state.refreshToken,
					user: state.user,
				}),
				onRehydrateStorage: () => (state) => {
					if (state && typeof window !== 'undefined') {
						state._setHydrated(true);

						// Check if tokens are still valid
						if (state.accessToken) {
							// Use async function to handle token validation
							(async () => {
								try {
									const user = await decodeToken(state.accessToken);
									if (!user) {
										// Token expired or invalid, clear auth data silently
										console.log('Clearing expired/invalid tokens from storage');
										state._clearAllData();
									} else {
										// Update the state with the validated user
										state.user = user;
									}
								} catch (error) {
									// Handle any unexpected errors during token validation
									console.log(
										'Clearing invalid auth data due to validation error',
									);
									state._clearAllData();
								}
							})();
						}
					} else if (typeof window === 'undefined') {
						// On server side, mark as hydrated immediately
						if (state) {
							state._setHydrated(true);
						}
					}
				},
			},
		),
	);
};
